# InvoiceDialog 组件使用说明

## 概述

InvoiceDialog 是一个多功能的发票对话框组件，支持两种模式：
1. **创建模式** (`mode="create"`) - 用于申请开票
2. **详情模式** (`mode="detail"`) - 用于查看发票详情

## 功能特性

### 创建模式功能
- 发票申请表单填写
- 发票抬头选择和管理
- 发票类型选择（增值税普通发票/专用发票）
- 开票备注填写
- 订单信息展示

### 详情模式功能
- Tab 切换（订单信息、物流信息、发票信息）
- 发票详情展示
- 基本信息显示
- 卡片式发票信息展示

## Props 参数

| 参数名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| visible | boolean | - | ✓ | 对话框显示状态 |
| orderNo | string | - | ✓ | 订单号 |
| orderData | any | - | ✓ | 订单数据 |
| invoiceHeaders | any[] | - | ✓ | 发票抬头列表 |
| invoiceTypes | any[] | - | ✗ | 可用发票类型 |
| mode | 'create' \| 'detail' | 'create' | ✗ | 组件模式 |
| invoiceId | string | '' | ✗ | 发票ID（详情模式必填） |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:visible | (value: boolean) | 更新对话框显示状态 |
| success | () | 操作成功回调 |

## 使用示例

### 申请开票模式

```vue
<template>
    <InvoiceDialog
        v-model:visible="dialogVisible"
        :order-no="orderNo"
        :order-data="orderData"
        :invoice-headers="invoiceHeaders"
        :invoice-types="invoiceTypes"
        mode="create"
        @success="handleSuccess"
    />
</template>

<script setup>
import InvoiceDialog from './InvoiceDialog.vue'

const dialogVisible = ref(false)
const orderNo = ref('SN002555245455444442')
const orderData = ref({
    billMoney: 2556, // 以分为单位
    invoiceInfo: [...],
    supplierName: '测试供应商'
})

const handleSuccess = () => {
    console.log('发票申请成功')
}
</script>
```

### 查看详情模式

```vue
<template>
    <InvoiceDialog
        v-model:visible="dialogVisible"
        :order-no="orderNo"
        :order-data="orderData"
        :invoice-headers="invoiceHeaders"
        mode="detail"
        :invoice-id="invoiceId"
        @success="handleSuccess"
    />
</template>

<script setup>
import InvoiceDialog from './InvoiceDialog.vue'

const dialogVisible = ref(false)
const invoiceId = ref('123') // 发票ID
const orderNo = ref('SN002555245455444442')

const handleSuccess = () => {
    console.log('操作成功')
}
</script>
```

## API 接口

组件内部使用以下 API 接口：

### 创建模式
- `doGetInvoiceHeadersList` - 获取发票抬头列表
- `doGetDefaultInvoiceHeader` - 获取默认发票抬头
- `doGetInvoiceHeaders` - 获取发票抬头详情
- `doPostInvoiceRequest` - 提交发票申请

### 详情模式
- `doGetinvoiceDetail` - 获取发票详情（调用 `/addon-invoice/invoice/invoiceRequest/{id}`）

## 数据结构

### orderData 结构
```typescript
interface OrderData {
    billMoney: number // 开票金额（分为单位）
    invoiceInfo?: Array<{
        orderNo: string
        name: string
        billMoney: number
        invoiceToType: 'PERSONAL' | 'ENTERPRISE'
    }>
    invoiceTypes?: string[]
    supplierName?: string
}
```

### 发票详情数据结构
```typescript
interface InvoiceDetail {
    orderNos: string
    invoiceAmount: number
    shopSupplierName: string
    invoiceInfo: Array<{
        name: string
        billMoney: number
        invoiceToType: 'PERSONAL' | 'ENTERPRISE'
        billingRemarks: string
        enterpriseAddress: string
        taxNo: string
        bankAccountNo: string
        enterprisePhone: string
        openingBank: string
    }>
}
```

## 注意事项

1. **发票ID**: 在详情模式下，必须提供有效的 `invoiceId`
2. **金额单位**: 所有金额都以分为单位传入，组件内部会自动转换为元显示
3. **API 地址**: 查看详情接口地址为 `http://*************:9999/addon-invoice/invoice/invoiceRequest/{id}`
4. **Tab 切换**: 详情模式下默认显示发票信息 tab，创建模式下默认显示订单信息 tab
5. **供应商端**: 组件设计为供应商端使用，`invoiceOwnerType` 固定为 'SUPPLIER'
