<template>
    <div class="invoice-dialog-example">
        <h2>发票对话框使用示例</h2>
        
        <div class="example-buttons">
            <el-button type="primary" @click="openCreateDialog">申请开票</el-button>
            <el-button type="success" @click="openDetailDialog">查看发票详情</el-button>
        </div>

        <!-- 申请开票对话框 -->
        <InvoiceDialog
            v-model:visible="createDialogVisible"
            :order-no="orderNo"
            :order-data="orderData"
            :invoice-headers="invoiceHeaders"
            :invoice-types="invoiceTypes"
            mode="create"
            @success="handleCreateSuccess"
        />

        <!-- 查看发票详情对话框 -->
        <InvoiceDialog
            v-model:visible="detailDialogVisible"
            :order-no="orderNo"
            :order-data="orderData"
            :invoice-headers="invoiceHeaders"
            mode="detail"
            :invoice-id="invoiceId"
            @success="handleDetailSuccess"
        />
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import InvoiceDialog from './InvoiceDialog.vue'

// 对话框显示状态
const createDialogVisible = ref(false)
const detailDialogVisible = ref(false)

// 示例数据
const orderNo = ref('SN002555245455444442')
const invoiceId = ref('123') // 示例发票ID，实际使用时应该是真实的发票ID

const orderData = ref({
    billMoney: 2556, // 以分为单位
    invoiceInfo: [
        {
            orderNo: 'SN002555245455444442',
            name: '第二家店铺',
            billMoney: 2556,
            invoiceToType: 'ENTERPRISE'
        }
    ],
    invoiceTypes: ['VAT_GENERAL', 'VAT_SPECIAL'],
    supplierName: '测试供应商'
})

const invoiceHeaders = ref([])
const invoiceTypes = ref(['VAT_GENERAL', 'VAT_SPECIAL'])

// 打开申请开票对话框
const openCreateDialog = () => {
    createDialogVisible.value = true
}

// 打开查看详情对话框
const openDetailDialog = () => {
    detailDialogVisible.value = true
}

// 处理申请开票成功
const handleCreateSuccess = () => {
    ElMessage.success('发票申请提交成功')
    // 这里可以添加刷新列表等逻辑
}

// 处理查看详情成功
const handleDetailSuccess = () => {
    ElMessage.success('操作成功')
    // 这里可以添加其他逻辑
}
</script>

<style scoped lang="scss">
.invoice-dialog-example {
    padding: 20px;

    h2 {
        margin-bottom: 20px;
        color: #333;
    }

    .example-buttons {
        display: flex;
        gap: 16px;
        margin-bottom: 20px;

        .el-button {
            padding: 12px 24px;
        }
    }
}
</style>
