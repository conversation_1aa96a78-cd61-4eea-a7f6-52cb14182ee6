<template>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="900px" @close="handleClose">
        <div class="invoice-dialog" v-loading="headerLoading || detailLoading" :element-loading-text="loadingText">
            <!-- 供应商信息和开票金额 -->
            <div class="header-section">
                <div class="supplier-info">
                    <span class="label">供应商名称：</span>
                    <span class="value">{{ supplierName || '测试供应商' }}</span>
                </div>
                <div class="invoice-amount">
                    <span class="label">开票金额</span>
                    <span class="amount">¥{{ formatMoney(currentBillMoney) }}</span>
                </div>
                <div class="order-date">
                    <span class="label">订单</span>
                </div>
            </div>

            <!-- Tab 切换 -->
            <el-tabs v-model="activeTab" class="invoice-tabs">
                <!-- 订单信息 tab -->
                <el-tab-pane label="订单信息" name="order">
                    <!-- 说明文字 -->
                    <div class="notice-text" v-if="mode === 'create'">
                        1. 开票金额为消费者支付金额，红包、优惠、购物券、消费返利等不可开具发票金额。2. 如开票发生错误信息、退款，开票金额出现内容错误金额时，
                    </div>

                    <!-- 表单区域 - 仅在创建模式下显示 -->
                    <el-form v-if="mode === 'create'" ref="formRef" :model="form" :rules="rules" label-width="80px" class="invoice-form">
                        <!-- 开票备注 -->
                        <div class="form-section">
                            <div class="form-row">
                                <span class="form-label">开票备注：</span>
                                <el-form-item prop="billingRemarks" class="form-item-inline">
                                    <el-input v-model="form.billingRemarks" placeholder="选填" class="remark-input" />
                                </el-form-item>
                            </div>

                            <!-- 发票类型 -->
                            <div class="form-row">
                                <span class="form-label">发票类型：</span>
                                <el-form-item prop="invoiceType" class="form-item-inline">
                                    <div class="radio-group">
                                        <el-radio-group v-model="form.invoiceType">
                                            <el-radio label="VAT_GENERAL">增值税电子普通发票</el-radio>
                                            <el-radio label="VAT_SPECIAL">增值税电子专用发票</el-radio>
                                        </el-radio-group>
                                    </div>
                                </el-form-item>
                            </div>

                            <!-- 抬头选择 -->
                            <div class="form-row">
                                <span class="form-label">抬头选择：</span>
                                <el-form-item prop="invoiceHeaderId" class="form-item-inline">
                                    <el-select v-model="form.invoiceHeaderId" placeholder="请选择抬头" class="header-select" clearable @change="handleHeaderChange">
                                        <el-option v-for="header in invoiceHeadersList" :key="header.id" :label="header.header" :value="header.id">
                                            <div class="header-option">
                                                <span class="header-name">{{ header.header }}</span>
                                                <span class="header-type">{{ header.invoiceHeaderType === 'ENTERPRISE' ? '企业' : '个人' }}</span>
                                            </div>
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </div>

                            <!-- 抬头类型 -->
                            <div class="form-row">
                                <span class="form-label">抬头类型：</span>
                                <span class="form-value">{{ form.headerType === 'PERSONAL' ? '个人' : '企业' }}</span>
                                <span class="form-label ml-60">发票抬头：</span>
                                <span class="form-value">{{ getHeaderName() }}</span>
                                <span class="form-label ml-60">税号：</span>
                                <span class="form-value">{{ form.taxNo || '************' }}</span>
                            </div>

                            <div class="form-row">
                                <span class="form-label">开户行：</span>
                                <span class="form-value">{{ form.openingBank || '第二家店铺开户行' }}</span>
                                <span class="form-label ml-60">银行账号：</span>
                                <span class="form-value">{{ form.bankAccountNo || '第二家店铺银行账号' }}</span>
                                <span class="form-label ml-60">企业电话：</span>
                                <span class="form-value">{{ form.enterprisePhone || '***********' }}</span>
                            </div>

                            <div class="form-row">
                                <span class="form-label">邮箱地址：</span>
                                <span class="form-value">{{ form.email || '<EMAIL>' }}</span>
                                <span class="form-label ml-60">企业地址：</span>
                                <span class="form-value">{{ form.enterpriseAddress || '第二家店铺企业地址' }}</span>
                            </div>
                        </div>
                    </el-form>

                    <!-- 发票详情 -->
                    <div class="invoice-details">
                        <div class="details-header">
                            <span class="header-title">发票详情</span>
                        </div>

                        <div class="details-table">
                            <div class="table-header">
                                <div class="col-order">订单号</div>
                                <div class="col-shop">开票方</div>
                                <div class="col-amount">开票金额</div>
                            </div>

                            <div class="table-body">
                                <div v-for="(order, index) in orderList" :key="index" class="table-row">
                                    <div class="col-order">{{ order.orderNo }}</div>
                                    <div class="col-shop">{{ order.shopName }}</div>
                                    <div class="col-amount">¥{{ order.amount }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>

                <!-- 物流信息 tab -->
                <el-tab-pane label="物流信息" name="logistics">
                    <div class="logistics-info">
                        <p>物流信息内容待实现</p>
                    </div>
                </el-tab-pane>

                <!-- 发票信息 tab -->
                <el-tab-pane label="发票信息" name="invoice">
                    <div v-if="mode === 'detail' && invoiceDetail" class="invoice-info">
                        <!-- 基本信息 -->
                        <div class="basic-info">
                            <h3>基本信息</h3>
                            <div class="info-row">
                                <span class="info-label">订单号：</span>
                                <span class="info-value">{{ invoiceDetail.orderNos || orderNo }}</span>
                                <span class="info-label ml-60">开票金额：</span>
                                <span class="info-value">¥{{ formatMoney(invoiceDetail.invoiceAmount) }}</span>
                            </div>
                        </div>

                        <!-- 发票详情 -->
                        <div class="invoice-detail-section">
                            <h3>发票详情</h3>
                            <div class="detail-cards">
                                <div v-for="(item, index) in invoiceDetailList" :key="index" class="detail-card">
                                    <div class="card-header">
                                        <span class="card-title">{{ item.name || '发票信息' }}</span>
                                        <span class="card-amount">金额：¥{{ formatMoney(item.billMoney) }}</span>
                                    </div>
                                    <div class="card-content">
                                        <div class="content-row">
                                            <span class="content-label">发票抬头：</span>
                                            <span class="content-value">{{ item.invoiceToType === 'PERSONAL' ? '个人' : '企业' }}</span>
                                            <span class="content-label ml-40">发票备注：</span>
                                            <span class="content-value">{{ item.billingRemarks || '无' }}</span>
                                        </div>
                                        <div class="content-row">
                                            <span class="content-label">发票地址：</span>
                                            <span class="content-value">{{ item.enterpriseAddress || 'xxxxxxxxxxxxx' }}</span>
                                            <span class="content-label ml-40">发票备注：</span>
                                            <span class="content-value">{{ item.billingRemarks || 'xxxxxxxxxxxxx' }}</span>
                                        </div>
                                        <div class="content-row">
                                            <span class="content-label">税号：</span>
                                            <span class="content-value">{{ item.taxNo || '************' }}</span>
                                            <span class="content-label ml-40">银行账号：</span>
                                            <span class="content-value">{{ item.bankAccountNo || '第二家店铺银行账号' }}</span>
                                        </div>
                                        <div class="content-row">
                                            <span class="content-label">企业电话：</span>
                                            <span class="content-value">{{ item.enterprisePhone || '***********' }}</span>
                                            <span class="content-label ml-40">开户行：</span>
                                            <span class="content-value">{{ item.openingBank || '第二家店铺开户行' }}</span>
                                        </div>
                                        <div class="content-row">
                                            <span class="content-label">企业地址：</span>
                                            <span class="content-value">{{ item.enterpriseAddress || '第二家店铺企业地址' }}</span>
                                        </div>
                                        <div class="content-footer">
                                            <span class="footer-text">拒绝原因：我来看看你的开票信息，你好好看看你的开票信息</span>
                                            <div class="footer-actions">
                                                <span class="action-date">撤销日期</span>
                                                <el-button type="primary" size="small" class="action-btn">开票成功</el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <div class="button-group">
                    <el-button v-if="mode === 'create'" type="primary" class="submit-btn" :loading="loading" @click="handleSubmit">确认</el-button>
                    <el-button class="cancel-btn" @click="handleClose">{{ mode === 'detail' ? '关闭' : '取消' }}</el-button>
                </div>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="InvoiceDialog">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { doPostInvoiceRequest, doGetDefaultInvoiceHeader, doGetInvoiceHeaders, doPostInvoiceHeader, doGetInvoiceHeadersList, doGetinvoiceDetail } from '@/apis/invoice'

interface Props {
    visible: boolean
    orderNo: string
    orderData: any
    invoiceHeaders: any[]
    invoiceTypes?: any[]
    mode?: 'create' | 'detail' // 新增模式属性
    invoiceId?: string // 新增发票ID属性，用于查看详情
}

interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
    mode: 'create',
    invoiceId: '',
})
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const headerLoading = ref(false)
const detailLoading = ref(false) // 新增详情加载状态
const defaultHeaderId = ref<number | null>(null)
const headerInfo = ref<any>(null)
const invoiceHeadersList = ref<any[]>([])
const invoiceDetail = ref<any>(null) // 新增发票详情数据
const activeTab = ref('order') // 新增当前激活的tab

// 新增的数据
const supplierName = ref('测试供应商')

// 计算属性
const dialogTitle = computed(() => {
    return props.mode === 'detail' ? '发票详情' : '申请开票'
})

const loadingText = computed(() => {
    if (detailLoading.value) return '正在加载发票详情...'
    if (headerLoading.value) return '正在加载抬头信息...'
    return '加载中...'
})

const currentBillMoney = computed(() => {
    if (props.mode === 'detail' && invoiceDetail.value) {
        return invoiceDetail.value.invoiceAmount || 0
    }
    return props.orderData?.billMoney || 0
})

// 发票详情列表（用于发票信息tab显示）
const invoiceDetailList = computed(() => {
    if (props.mode === 'detail' && invoiceDetail.value) {
        // 如果有详情数据，使用详情数据
        return invoiceDetail.value.invoiceInfo || []
    }
    return []
})

// 订单列表数据（模拟数据，实际应该从props.orderData中获取）
const orderList = computed(() => {
    if (props.orderData?.invoiceInfo && props.orderData.invoiceInfo.length > 0) {
        return props.orderData.invoiceInfo.map((info: any) => ({
            orderNo: info.orderNo || props.orderNo,
            shopName: info.name || '店铺名称',
            amount: formatMoney(info.billMoney || props.orderData.billMoney),
        }))
    }

    // 默认数据
    return [
        { orderNo: 'DENNINNKCIKNKNANN87892222222', shopName: '【供应商】店铺名称', amount: '25.52' },
        { orderNo: 'SK169923358878888888888888888', shopName: '【商家】店铺名称', amount: '25.52' },
        { orderNo: 'YT125987456398227812224878455', shopName: '【平台】党有灵犀', amount: '25.52' },
        { orderNo: 'YT125987456398227812224878455', shopName: '【商家】店铺名称', amount: '25.52' },
    ]
})

// 处理弹窗显示状态的计算属性
const dialogVisible = computed({
    get: () => props.visible,
    set: (value: boolean) => emit('update:visible', value),
})

// 可用的发票类型
const availableInvoiceTypes = computed(() => {
    return props.invoiceTypes || props.orderData?.invoiceTypes || ['VAT_GENERAL']
})

// 格式化金额显示
const formatMoney = (money: string | number) => {
    if (!money) return '0.00'
    const amount = typeof money === 'string' ? parseFloat(money) : money
    return (amount / 100).toFixed(2) // 假设后端返回的是分为单位
}

// 获取发票类型标签
const getInvoiceTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
        VAT_GENERAL: '增值税普通发票',
        VAT_SPECIAL: '增值税专用发票',
    }
    return labels[type] || type
}

// 表单数据
const form = reactive({
    billingRemarks: '',
    invoiceType: 'VAT_GENERAL',
    headerType: 'COMPANY', // 默认企业类型
    personalName: '',
    companyName: '第二家店铺',
    taxIdentNo: '************', // 税号
    openingBank: '第二家店铺开户行', // 开户行
    bankAccountNo: '第二家店铺银行账号', // 银行账号
    enterpriseAddress: '第二家店铺企业地址', // 企业地址
    enterprisePhone: '***********', // 企业电话
    email: '<EMAIL>',
    invoiceHeaderId: 0,
    // 保留旧字段名以兼容现有代码
    taxNo: '************',
    bankName: '第二家店铺开户行',
    bankAccount: '第二家店铺银行账号',
    companyAddress: '第二家店铺企业地址',
    companyPhone: '***********',
})

// 获取抬头名称
const getHeaderName = () => {
    if (form.headerType === 'PERSONAL') {
        return form.personalName || '个人姓名'
    } else {
        return form.companyName || '第二家店铺'
    }
}

// 表单验证规则
const rules = computed(() => ({
    // 开票备注不是必填项，根据截图显示为"选填"
    billingRemarks: [],
    invoiceType: [{ required: true, message: '请选择发票类型', trigger: 'change' }],
    invoiceHeaderId: [{ required: true, message: '请选择发票抬头', trigger: 'change' }],
}))

/**
 * 加载发票详情
 */
const loadInvoiceDetail = async () => {
    if (!props.invoiceId) {
        console.error('发票ID不能为空')
        return
    }

    try {
        detailLoading.value = true
        console.log('加载发票详情, ID:', props.invoiceId)

        const { code, data, msg } = await doGetinvoiceDetail(props.invoiceId)

        if (code === 200 && data) {
            console.log('发票详情查询成功:', data)
            invoiceDetail.value = data

            // 如果有供应商信息，更新供应商名称
            if (data.shopSupplierName) {
                supplierName.value = data.shopSupplierName
            }
        } else {
            console.error('发票详情查询失败:', msg)
            ElMessage.error(msg || '获取发票详情失败')
        }
    } catch (error) {
        console.error('加载发票详情失败:', error)
        ElMessage.error('获取发票详情失败，请稍后重试')
    } finally {
        detailLoading.value = false
    }
}

/**
 * 加载发票抬头列表
 */
const loadInvoiceHeadersList = async () => {
    try {
        console.log('加载发票抬头列表...')

        const { code, data, msg } = await doGetInvoiceHeadersList({
            ownerType: 'SUPPLIER',
            current: 1,
            size: 100,
        })

        if (code === 200 && data) {
            console.log('抬头列表查询成功:', data)
            invoiceHeadersList.value = data.records || data || []
        } else {
            console.log('抬头列表查询失败:', msg)
            invoiceHeadersList.value = []
        }
    } catch (error) {
        console.error('加载抬头列表失败:', error)
        invoiceHeadersList.value = []
    }
}

/**
 * 处理抬头选择变化
 */
const handleHeaderChange = async (headerId: number) => {
    if (!headerId) {
        // 清空选择
        headerInfo.value = null
        resetFormHeaderData()
        return
    }

    // 从列表中找到选中的抬头
    const selectedHeader = invoiceHeadersList.value.find((header) => header.id === headerId)
    if (selectedHeader) {
        console.log('选中抬头:', selectedHeader)
        headerInfo.value = selectedHeader
        fillFormWithHeaderInfo(selectedHeader)
    } else {
        // 如果列表中没有，则通过API查询详细信息
        await loadInvoiceHeaderInfo(headerId)
    }
}

/**
 * 重置表单抬头数据
 */
const resetFormHeaderData = () => {
    form.headerType = 'COMPANY'
    form.personalName = ''
    form.companyName = '第二家店铺'
    // 新字段名
    form.taxIdentNo = '************'
    form.openingBank = '第二家店铺开户行'
    form.bankAccountNo = '第二家店铺银行账号'
    form.enterpriseAddress = '第二家店铺企业地址'
    form.enterprisePhone = '***********'
    form.email = '<EMAIL>'
    // 旧字段名（保持兼容性）
    form.taxNo = '************'
    form.bankName = '第二家店铺开户行'
    form.bankAccount = '第二家店铺银行账号'
    form.companyAddress = '第二家店铺企业地址'
    form.companyPhone = '***********'
    form.invoiceHeaderId = 0
}

/**
 * 查询默认发票抬头
 */
const loadDefaultInvoiceHeader = async () => {
    try {
        headerLoading.value = true
        console.log('查询默认发票抬头...')

        const { code, data, msg } = await doGetDefaultInvoiceHeader()

        if (code === 200 && data) {
            console.log('默认抬头查询成功:', data)
            defaultHeaderId.value = data.id || data.headerId

            // 如果有默认抬头ID，查询详细信息
            if (defaultHeaderId.value) {
                await loadInvoiceHeaderInfo(defaultHeaderId.value)
            }
        } else {
            console.log('没有默认抬头或查询失败:', msg)
            // 没有默认抬头，使用空表单
            defaultHeaderId.value = null
            headerInfo.value = null
        }
    } catch (error) {
        console.error('查询默认抬头失败:', error)
        defaultHeaderId.value = null
        headerInfo.value = null
    } finally {
        headerLoading.value = false
    }
}

/**
 * 查询抬头详细信息
 */
const loadInvoiceHeaderInfo = async (headerId: number) => {
    try {
        console.log('查询抬头详细信息, ID:', headerId)

        const { code, data, msg } = await doGetInvoiceHeaders(headerId.toString())

        if (code === 200 && data) {
            console.log('抬头信息查询成功:', data)
            headerInfo.value = data

            // 预填表单数据
            fillFormWithHeaderInfo(data)
        } else {
            console.error('抬头信息查询失败:', msg)
        }
    } catch (error) {
        console.error('查询抬头信息失败:', error)
    }
}

/**
 * 用抬头信息填充表单
 */
const fillFormWithHeaderInfo = (headerData: any) => {
    if (!headerData) return

    console.log('填充表单数据:', headerData)

    // 设置抬头类型 - 根据API字段映射
    form.headerType = headerData.invoiceHeaderType === 'ENTERPRISE' ? 'COMPANY' : 'PERSONAL'

    if (headerData.invoiceHeaderType === 'PERSONAL') {
        form.personalName = headerData.header || ''
    } else {
        form.companyName = headerData.header || '第二家店铺'
        // 使用新字段名
        form.taxIdentNo = headerData.taxIdentNo || '************'
        form.openingBank = headerData.openingBank || '第二家店铺开户行'
        form.bankAccountNo = headerData.bankAccountNo || '第二家店铺银行账号'
        form.enterpriseAddress = headerData.enterpriseAddress || '第二家店铺企业地址'
        form.enterprisePhone = headerData.enterprisePhone || '***********'
        form.email = headerData.email || '<EMAIL>'

        // 同时更新旧字段名以保持兼容性
        form.taxNo = headerData.taxIdentNo || '************'
        form.bankName = headerData.openingBank || '第二家店铺开户行'
        form.bankAccount = headerData.bankAccountNo || '第二家店铺银行账号'
        form.companyAddress = headerData.enterpriseAddress || '第二家店铺企业地址'
        form.companyPhone = headerData.enterprisePhone || '***********'
    }

    // 设置抬头ID
    form.invoiceHeaderId = headerData.id || 0
}

/**
 * 创建新的发票抬头
 */
const createInvoiceHeader = async () => {
    try {
        const headerData = {
            invoiceHeaderType: (form.headerType === 'COMPANY' ? 'ENTERPRISE' : 'PERSONAL') as 'ENTERPRISE' | 'PERSONAL',
            header: form.headerType === 'PERSONAL' ? form.personalName : form.companyName,
            taxIdentNo: form.headerType === 'COMPANY' ? form.taxIdentNo : undefined,
            openingBank: form.headerType === 'COMPANY' ? form.openingBank : undefined,
            bankAccountNo: form.headerType === 'COMPANY' ? form.bankAccountNo : undefined,
            enterpriseAddress: form.headerType === 'COMPANY' ? form.enterpriseAddress : undefined,
            enterprisePhone: form.headerType === 'COMPANY' ? form.enterprisePhone : undefined,
            email: form.email,
            isDefault: !defaultHeaderId.value, // 如果没有默认抬头，设为默认
        }

        console.log('创建发票抬头:', headerData)

        const { code, data, msg } = await doPostInvoiceHeader(headerData)

        if (code === 200 && data) {
            console.log('抬头创建成功:', data)
            form.invoiceHeaderId = data.id || data.headerId
            return data.id || data.headerId
        } else {
            console.error('抬头创建失败:', msg)
            throw new Error(msg || '抬头创建失败')
        }
    } catch (error) {
        console.error('创建抬头失败:', error)
        throw error
    }
}

// 处理抬头类型变化
const handleHeaderTypeChange = () => {
    // 清空相关字段
    if (form.headerType === 'PERSONAL') {
        form.companyName = ''
        form.taxNo = ''
        form.bankName = ''
        form.bankAccount = ''
        form.companyAddress = ''
        form.companyPhone = ''
    } else {
        form.personalName = ''
    }

    // 重置抬头ID
    form.invoiceHeaderId = 0
}

// 关闭弹窗
const handleClose = () => {
    emit('update:visible', false)
    // 重置表单
    formRef.value?.resetFields()
}

// 提交申请
const handleSubmit = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()
        loading.value = true

        // 确保有有效的抬头ID
        let headerId = form.invoiceHeaderId

        // 如果没有抬头ID，需要先创建抬头
        if (!headerId || headerId === 0) {
            console.log('没有抬头ID，创建新抬头...')
            headerId = await createInvoiceHeader()
        }

        const requestData = {
            invoiceOwnerType: 'SUPPLIER', // 发票所属类型（供应商端固定为SUPPLIER）
            orderNos: props.orderData?.orderNos || [props.orderNo], // 订单号集合
            invoiceType: form.invoiceType, // 发票类型
            billingRemarks: form.billingRemarks || '', // 开票备注（可选，但确保不为undefined）
            invoiceHeaderId: headerId, // 发票抬头ID
        }

        console.log('提交发票申请:', requestData)
        console.log('接口地址: http://192.168.1.254:9999/addon-invoice/invoice/invoiceRequest')

        const { code, msg } = await doPostInvoiceRequest(requestData)

        if (code === 200) {
            ElMessage.success('发票申请提交成功')
            emit('success')
            handleClose()
        } else {
            ElMessage.error(msg || '发票申请提交失败')
        }
    } catch (error) {
        console.error('发票申请失败:', error)
        ElMessage.error('发票申请提交失败，请稍后重试')
    } finally {
        loading.value = false
    }
}

// 监听visible变化
watch(
    () => props.visible,
    async (newVal) => {
        if (newVal) {
            console.log('发票弹窗打开, 模式:', props.mode)

            if (props.mode === 'detail') {
                // 详情模式：加载发票详情
                activeTab.value = 'invoice' // 默认显示发票信息tab
                await loadInvoiceDetail()
            } else {
                // 创建模式：初始化表单数据
                activeTab.value = 'order' // 默认显示订单信息tab

                // 弹窗打开时，预填一些数据
                console.log('发票弹窗数据:', props.orderData)
                console.log('可用发票类型:', availableInvoiceTypes.value)

                // 预设第一个可用的发票类型
                if (availableInvoiceTypes.value.length > 0) {
                    form.invoiceType = availableInvoiceTypes.value[0]
                }

                // 先加载抬头列表
                await loadInvoiceHeadersList()

                // 然后尝试加载默认抬头信息
                await loadDefaultInvoiceHeader()

                // 如果没有默认抬头，但有订单中的发票信息，则使用订单信息
                if (!headerInfo.value && props.orderData?.invoiceInfo && props.orderData.invoiceInfo.length > 0) {
                    const invoiceInfo = props.orderData.invoiceInfo[0]
                    console.log('使用订单中的发票信息:', invoiceInfo)

                    if (invoiceInfo.invoiceToType === 'PERSONAL') {
                        form.headerType = 'PERSONAL'
                        form.personalName = invoiceInfo.name
                    } else {
                        form.headerType = 'COMPANY'
                        form.companyName = invoiceInfo.name || '第二家店铺'
                    }
                }

                // 如果有供应商信息，更新供应商名称
                if (props.orderData?.supplierName) {
                    supplierName.value = props.orderData.supplierName
                }
            }
        }
    },
)
</script>

<style scoped lang="scss">
.invoice-dialog {
    font-size: 14px;
    color: #333;

    .header-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #eee;

        .supplier-info {
            .label {
                color: #666;
                margin-right: 8px;
            }
            .value {
                color: #333;
                font-weight: 500;
            }
        }

        .invoice-amount {
            text-align: center;

            .label {
                display: block;
                color: #666;
                font-size: 14px;
                margin-bottom: 4px;
            }

            .amount {
                color: #ff4d4f;
                font-size: 20px;
                font-weight: bold;
            }
        }

        .order-date {
            .label {
                color: #666;
            }
        }
    }

    .notice-text {
        color: #666;
        font-size: 12px;
        line-height: 1.5;
        margin-bottom: 20px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 4px;
    }

    .form-section {
        margin-bottom: 24px;

        .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            min-height: 32px;

            .form-label {
                color: #333;
                min-width: 80px;
                margin-right: 12px;
                font-weight: 500;

                &.ml-60 {
                    margin-left: 60px;
                }
            }

            .form-value {
                color: #666;
                margin-right: 20px;
            }

            .remark-input {
                width: 300px;
            }

            .header-select {
                width: 200px;
            }

            .header-option {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .header-name {
                    flex: 1;
                    color: #333;
                }

                .header-type {
                    color: #999;
                    font-size: 12px;
                    margin-left: 8px;
                }
            }

            .radio-group {
                :deep(.el-radio) {
                    margin-right: 24px;

                    .el-radio__label {
                        color: #333;
                    }
                }
            }
        }
    }

    .invoice-details {
        .details-header {
            margin-bottom: 12px;

            .header-title {
                color: #333;
                font-weight: 500;
                font-size: 16px;
            }
        }

        .details-table {
            border: 1px solid #e4e7ed;
            border-radius: 4px;

            .table-header {
                display: flex;
                background: #f5f7fa;
                border-bottom: 1px solid #e4e7ed;
                font-weight: 500;
                color: #333;

                > div {
                    padding: 12px 16px;
                    border-right: 1px solid #e4e7ed;

                    &:last-child {
                        border-right: none;
                    }
                }
            }

            .table-body {
                .table-row {
                    display: flex;
                    border-bottom: 1px solid #e4e7ed;

                    &:last-child {
                        border-bottom: none;
                    }

                    > div {
                        padding: 12px 16px;
                        border-right: 1px solid #e4e7ed;
                        color: #666;

                        &:last-child {
                            border-right: none;
                        }
                    }
                }
            }

            .col-order {
                flex: 2;
                min-width: 200px;
            }

            .col-shop {
                flex: 1;
                min-width: 150px;
            }

            .col-amount {
                flex: 1;
                min-width: 100px;
                text-align: right;
            }
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: center;
    padding-top: 16px;
    border-top: 1px solid #eee;

    .button-group {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 12px;
        width: 100%;
    }

    .el-button {
        width: 140px !important;
        height: 40px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        margin: 0 !important;
        padding: 0 !important;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
    }

    .submit-btn {
        background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
        border: none;
        color: white;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);

        &:hover {
            background: linear-gradient(135deg, #337ecc 0%, #529b2e 100%);
            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
            transform: translateY(-1px);
        }

        &:active {
            transform: translateY(0);
        }
    }

    .cancel-btn {
        background: #f5f7fa;
        border: 1px solid #dcdfe6;
        color: #606266;

        &:hover {
            background: #ecf5ff;
            border-color: #409eff;
            color: #409eff;
        }
    }
}

// 表单样式
.invoice-form {
    .form-item-inline {
        margin-bottom: 0;

        :deep(.el-form-item__content) {
            margin-left: 0 !important;
        }

        :deep(.el-form-item__error) {
            position: static;
            margin-top: 4px;
        }
    }
}

// Tab 样式
.invoice-tabs {
    margin-top: 16px;

    :deep(.el-tabs__header) {
        margin-bottom: 20px;
    }

    :deep(.el-tabs__nav-wrap) {
        padding: 0 16px;
    }

    :deep(.el-tabs__item) {
        font-size: 14px;
        font-weight: 500;
        color: #666;

        &.is-active {
            color: #409eff;
        }
    }
}

// 发票信息tab样式
.invoice-info {
    .basic-info {
        margin-bottom: 24px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;

        h3 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .info-row {
            display: flex;
            align-items: center;

            .info-label {
                color: #666;
                margin-right: 8px;
                font-weight: 500;

                &.ml-60 {
                    margin-left: 60px;
                }
            }

            .info-value {
                color: #333;
                margin-right: 20px;
            }
        }
    }

    .invoice-detail-section {
        h3 {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .detail-cards {
            .detail-card {
                margin-bottom: 16px;
                border: 1px solid #e4e7ed;
                border-radius: 8px;
                overflow: hidden;

                .card-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 12px 16px;
                    background: #f5f7fa;
                    border-bottom: 1px solid #e4e7ed;

                    .card-title {
                        font-weight: 500;
                        color: #333;
                    }

                    .card-amount {
                        color: #ff4d4f;
                        font-weight: 500;
                    }
                }

                .card-content {
                    padding: 16px;

                    .content-row {
                        display: flex;
                        align-items: center;
                        margin-bottom: 12px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .content-label {
                            color: #666;
                            margin-right: 8px;
                            min-width: 80px;
                            font-weight: 500;

                            &.ml-40 {
                                margin-left: 40px;
                            }
                        }

                        .content-value {
                            color: #333;
                            margin-right: 20px;
                        }
                    }

                    .content-footer {
                        margin-top: 16px;
                        padding-top: 16px;
                        border-top: 1px solid #f0f0f0;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        .footer-text {
                            color: #ff4d4f;
                            font-size: 12px;
                        }

                        .footer-actions {
                            display: flex;
                            align-items: center;
                            gap: 12px;

                            .action-date {
                                color: #999;
                                font-size: 12px;
                            }

                            .action-btn {
                                padding: 4px 12px;
                                font-size: 12px;
                            }
                        }
                    }
                }
            }
        }
    }
}

// 物流信息tab样式
.logistics-info {
    padding: 20px;
    text-align: center;
    color: #999;
}

// Element Plus 样式覆盖
:deep(.el-dialog__header) {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #eee;
}

:deep(.el-dialog__body) {
    padding: 24px;
}

:deep(.el-dialog__footer) {
    padding: 16px 24px 20px;
}
</style>
